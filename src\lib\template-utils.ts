// Client-side utility functions for templates (no Node.js dependencies)

// Extract placeholders from HTML content
export function extractPlaceholders(htmlContent: string): string[] {
  const placeholderRegex = /\[([^\]]+)\]/g;
  const placeholders = new Set<string>();
  let match;

  while ((match = placeholderRegex.exec(htmlContent)) !== null) {
    // Normalize whitespace within the placeholder content
    const normalizedContent = match[1].replace(/\s+/g, ' ').trim();
    const normalizedPlaceholder = `[${normalizedContent}]`;
    placeholders.add(normalizedPlaceholder);
  }

  return Array.from(placeholders).sort();
}

// Count images that need to be loaded from HTML content
export function countImagesInHtml(htmlContent: string): number {
  // Match img tags with src attributes that are not empty and not data URLs
  const imgRegex = /<img[^>]+src\s*=\s*["']([^"']+)["'][^>]*>/gi;
  const images = [];
  let match;

  while ((match = imgRegex.exec(htmlContent)) !== null) {
    const src = match[1];
    // Skip data URLs, empty sources, and already absolute URLs
    if (src && !src.startsWith('data:') && !src.startsWith('http') && !src.startsWith('/')) {
      images.push(src);
    }
  }

  return images.length;
}

// Extract image sources from HTML content
export function extractImageSources(htmlContent: string): string[] {
  const imgRegex = /<img[^>]+src\s*=\s*["']([^"']+)["'][^>]*>/gi;
  const images = [];
  let match;

  while ((match = imgRegex.exec(htmlContent)) !== null) {
    const src = match[1];
    // Skip data URLs, empty sources, and already absolute URLs
    if (src && !src.startsWith('data:') && !src.startsWith('http') && !src.startsWith('/')) {
      images.push(src);
    }
  }

  return images;
}

// Update image paths in HTML content to point to uploaded folder
export function updateImagePaths(htmlContent: string, templateId: string): string {
  return htmlContent.replace(
    /<img([^>]+)src\s*=\s*["']([^"']+)["']([^>]*>)/gi,
    (match, beforeSrc, src, afterSrc) => {
      // Skip data URLs, empty sources, and already absolute URLs
      if (!src || src.startsWith('data:') || src.startsWith('http') || src.startsWith('/')) {
        return match;
      }

      // Update the src to point to the uploaded folder
      const newSrc = `/${templateId}/${src}`;
      return `<img${beforeSrc}src="${newSrc}"${afterSrc}`;
    }
  );
}

// Detect if HTML content contains an image with alt text "applicants_photo"
export function hasApplicantPhoto(htmlContent: string): boolean {
  const imgRegex = /<img[^>]*alt\s*=\s*["'][^"']*applicants_photo[^"']*["'][^>]*>/gi;
  return imgRegex.test(htmlContent);
}

// Extract dimensions of applicant photo from template
export function getApplicantPhotoDimensions(htmlContent: string): { width: number; height: number } | null {
  const imgRegex = /<img([^>]*?)alt\s*=\s*["'][^"']*applicants_photo[^"']*["']([^>]*?)>/gi;
  const match = imgRegex.exec(htmlContent);

  if (!match) return null;

  const fullImgTag = match[0];

  // Extract width and height from style attribute or width/height attributes
  const styleMatch = fullImgTag.match(/style\s*=\s*["']([^"']*?)["']/i);
  const widthAttrMatch = fullImgTag.match(/width\s*=\s*["']?(\d+)["']?/i);
  const heightAttrMatch = fullImgTag.match(/height\s*=\s*["']?(\d+)["']?/i);

  let width = 150; // default width
  let height = 200; // default height

  if (styleMatch) {
    const style = styleMatch[1];
    const widthStyleMatch = style.match(/width\s*:\s*(\d+)px/i);
    const heightStyleMatch = style.match(/height\s*:\s*(\d+)px/i);

    if (widthStyleMatch) width = parseInt(widthStyleMatch[1]);
    if (heightStyleMatch) height = parseInt(heightStyleMatch[1]);
  }

  if (widthAttrMatch) width = parseInt(widthAttrMatch[1]);
  if (heightAttrMatch) height = parseInt(heightAttrMatch[1]);

  return { width, height };
}

// Automatically process and fit image to target dimensions (center crop)
export function processImageToFit(file: File, targetWidth: number, targetHeight: number): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }

    img.onload = () => {
      // Set canvas to target dimensions
      canvas.width = targetWidth;
      canvas.height = targetHeight;

      // Calculate scaling to fill the target area (like object-fit: cover)
      const scaleX = targetWidth / img.width;
      const scaleY = targetHeight / img.height;
      const scale = Math.max(scaleX, scaleY); // Use larger scale to fill

      // Calculate dimensions after scaling
      const scaledWidth = img.width * scale;
      const scaledHeight = img.height * scale;

      // Calculate position to center the image
      const x = (targetWidth - scaledWidth) / 2;
      const y = (targetHeight - scaledHeight) / 2;

      // Fill with white background (optional)
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, targetWidth, targetHeight);

      // Draw the scaled and centered image
      ctx.drawImage(img, x, y, scaledWidth, scaledHeight);

      // Convert canvas to blob and create new file
      canvas.toBlob((blob) => {
        if (!blob) {
          reject(new Error('Failed to process image'));
          return;
        }

        const processedFile = new File([blob], file.name, {
          type: file.type,
          lastModified: Date.now(),
        });
        resolve(processedFile);
      }, file.type, 0.9);
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}

// Group placeholders by category for better form organization
export function groupPlaceholders(placeholders: string[]): Record<string, string[]> {
  const groups: Record<string, string[]> = {
    'Personal Information': [],
    'Date Information': [],
    'Contact Information': [],
    'Document Information': [],
    'Other Information': []
  };

  placeholders.forEach(placeholder => {
    const key = placeholder.replace(/[\[\]]/g, '').toLowerCase();

    // Name-related and personal fields
    if (key.includes('name') || key.includes('first') || key.includes('last') ||
        key.includes('middle') || key.includes('initial') || key.includes('surname') ||
        key.includes('age')) {
      groups['Personal Information'].push(placeholder);
    }
    // Date-related fields
    else if (key.includes('date') || key.includes('day') || key.includes('month') ||
             key.includes('year') || key.includes('time')) {
      groups['Date Information'].push(placeholder);
    }
    // Contact-related fields
    else if (key.includes('email') || key.includes('phone') || key.includes('address') ||
             key.includes('contact') || key.includes('mobile') || key.includes('tel')) {
      groups['Contact Information'].push(placeholder);
    }
    // Document-related fields
    else if (key.includes('number') || key.includes('id') || key.includes('tin') ||
             key.includes('ctc') || key.includes('o.r') || key.includes('receipt') ||
             key.includes('document') || key.includes('certificate')) {
      groups['Document Information'].push(placeholder);
    }
    // Everything else
    else {
      groups['Other Information'].push(placeholder);
    }
  });

  // Remove empty groups
  Object.keys(groups).forEach(groupName => {
    if (groups[groupName].length === 0) {
      delete groups[groupName];
    }
  });

  return groups;
}

// Replace applicant photo in HTML content with uploaded photo
export function replaceApplicantPhoto(htmlContent: string, photoPath: string): string {
  return htmlContent.replace(
    /<img([^>]*?)alt\s*=\s*["'][^"']*applicants_photo[^"']*["']([^>]*?)>/gi,
    (_, beforeAlt, afterAlt) => {
      // Extract existing attributes but replace the src
      const srcRegex = /src\s*=\s*["'][^"']*["']/gi;
      let attributes = beforeAlt + afterAlt;

      // Remove existing src attribute if present
      attributes = attributes.replace(srcRegex, '');

      // Add the new src attribute with proper styling for consistent display
      return `<img${attributes} src="${photoPath}" alt="applicants_photo" style="object-fit: cover; object-position: center;">`;
    }
  );
}

// Clean Word HTML content (client-side version without complex cleaning)
export function cleanWordHtmlBasic(htmlContent: string): string {
  let cleaned = htmlContent;

  // Remove Word-specific XML namespaces and tags
  cleaned = cleaned.replace(/<\?xml[^>]*>/gi, '');
  cleaned = cleaned.replace(/<\/?o:[^>]*>/gi, '');
  cleaned = cleaned.replace(/<\/?w:[^>]*>/gi, '');
  cleaned = cleaned.replace(/<\/?m:[^>]*>/gi, '');
  cleaned = cleaned.replace(/<\/?v:[^>]*>/gi, '');

  // Remove Word-specific attributes
  cleaned = cleaned.replace(/\s*mso-[^=]*="[^"]*"/gi, '');
  cleaned = cleaned.replace(/\s*class="Mso[^"]*"/gi, '');

  // Clean up extra whitespace
  cleaned = cleaned.replace(/\n\s*\n\s*\n/g, '\n\n');

  return cleaned.trim();
}

// Generate a unique filename for a template
export function generateTemplateFilename(name: string): string {
  const slug = name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');

  return `${slug}.html`;
}
